{{-- header start --}}
<header class="header">
    <nav class="navbar navbar-expand-xl navbar-light navbar-light py-0">
        <div class="container">
            <a class="navbar-brand" href="{{ url('/' . app()->getLocale()) }}"><img
                    src="{{ asset(common_setting()->dashboard_logo ?? '/images/logo.png') }}"
                    alt="logo" class="img-fluid">
            </a>
            {{-- <a class="navbar-brand" href="{{ url('/') }}"><img src=" {{ asset('website') }}/images/logo.png"
                    alt="logo" class="img-fluid"></a> --}}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
                aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                <span class="">
                    <i class="fas fa-bars"></i>
                </span>
            </button>
            <div class="search_parent">
                <form action="{{ route('listing_search_form') }}" method="GET" class="search d-flex ms-3 px-3 ">
                    <input type="text" id="search-listing-inp" name="listing_name"
                        value="{{ request()->listing_name }}" required class="form-control notranslate"
                        placeholder="{{ translate('home.home_search_bar') }}" autocomplete="off">
                    <button class="btn"><i class="fa fa-search dark-yellow"></i></button>
                </form>
                <div class="search_dropdown" id="listing-search-dropdown" style="display: none">
                    {{-- <div class="d-flex justify-content-end">
                        <button class="btn btn-sm" id="close-listing-search"><i class="fa fa-times"></i></button>

                    </div> --}}
                    <ul class="list-unstyled m-0 listig_search_parent">
                    </ul>
                </div>
            </div>

            <div class="collapse navbar-collapse justify-content-end ps-1" id="navbarNavDropdown">
                <ul class="navbar-nav">
                    {{-- Home Icon --}}
                    <li class="nav-item @if (request()->is(app()->getLocale()) || request()->is('/')) active @endif">
                        <a class="nav-link " aria-current="page" href="{{ url('/' . app()->getLocale()) }}" flow="down" tooltip="{{ translate('header.home') }}">
                            <img src="{{ asset('website') }}/images/home.svg" height="24px" width="24px">
                        </a>
                    </li>
                    {{-- Inbox Icon --}}
                    <li class="nav-item inbox position-relative @if (request()->is('message*')) active @endif">
                        @auth
                            {{-- <a class="nav-link" href="{{ route('inboxChat', 2) }}"> --}}
                            <a class="nav-link" href="{{ route('message.index') }}" flow="down" tooltip="{{ translate('header.inbox') }}">
                                <img src="{{ asset('website') }}/images/mail.svg" height="26px" width="26px">
                            </a>
                            {{-- Unread messages count --}}
                            @if (!Route::is('message.index'))
                                <div class="badge position-absolute" id="message-badge" style="display: {{ auth()->user()->unread_messages_count() > 0 ? 'block' : 'none' }};">
                                    <p class="fs-12" id="message-count">
                                        {{ auth()->user()->unread_messages_count() > 9 ? '9+' : auth()->user()->unread_messages_count() }}
                                    </p>
                                </div>
                            @endif
                        @else
                            <a class="nav-link" data-bs-toggle="modal" data-bs-target="#login" flow="down" tooltip="{{ translate('header.inbox') }}">
                                <img src="{{ asset('website') }}/images/mail.svg" height="26px" width="26px">
                            </a>
                        @endauth
                    </li>
                    {{-- Wishlist Icon --}}
                    <li class="nav-item @if (request()->is('wishlist')) active @endif">
                        @auth
                            <a class="nav-link wishlist position-relative" href="{{ route('wishlist', ["locale" => app()->getLocale()]) }}" flow="down" tooltip="{{ translate('header.wishlist') }}">
                                <img src="{{ asset('website') }}/images/heart.svg" width="24px" height="24px">
                                {{-- @if (auth()->user()->wishlists()->count()) --}}
                                    <div class="badge position-absolute">
                                        <p class="fs-12" id="wishlist_count" data-count="{{ auth()->user()->wishlists()->count() > 9 ? '9+' : auth()->user()->wishlists()->count() }}">
                                            {{ auth()->user()->wishlists()->count() > 9 ? '9+' : auth()->user()->wishlists()->count() }}
                                        </p>
                                    </div>
                                {{-- @endif --}}
                            </a>
                        @else
                            <a class="nav-link wishlist position-relative" data-bs-toggle="modal" data-bs-target="#login" flow="down" tooltip="{{ translate('header.wishlist') }}">
                                <img src="{{ asset('website') }}/images/heart.svg" width="24px" height="24px">
                            </a>
                        @endauth
                    </li>
                    {{-- Currency --}}
                    <li class="nav-item ">
                        <div class="dropdown globe currency_list">
                            <a class="nav-link" href="#" role="button" id="currencyDropdown" class="nav-link"
                                data-bs-toggle="dropdown" aria-expanded="false" flow="down" tooltip="{{ translate('header.currency') }}">
                                <img src="{{ asset('website') }}/images/money-bag.svg" width="24px" height="24px">
                            </a>
                            <div class="dropdown-menu" aria-labelledby="languageDropdown">
                                <input type="text" class="form-control mx-2 mb-2 search_curreny"
                                    name="search_currency" id="search_currency" />
                                <ul class="currency_parent list-unstyled">
                                    @foreach (config('constant.currencies') ?? [] as $code => $currency)
                                        <li>
                                            <a class="dropdown-item lang-pic d-flex align-items-center gap-1"
                                                href="{{ route('currency', $code) }}">
                                                <div class="flag_img">
                                                    @if (isset($currency['icon']))
                                                        <i class="fas {{ $currency['icon'] }}"
                                                            style="font-size: 1.5rem;"></i>
                                                    @else
                                                        <span>{{ $code }}</span>
                                                        <!-- Fallback if icon not available -->
                                                    @endif
                                                </div>
                                                <p class="m-0 notranslate currency_name">{{ $currency['label'] }}
                                                    ({{ $code }})
                                                </p>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </li>
                    {{-- Globe Icon --}}
                    <li class="nav-item ">
                        <div class="dropdown globe">
                            <a class="nav-link" href="#" role="button" id="languageDropdown" flow="down" tooltip="{{ translate('header.language') }}"
                                class="nav-link" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="{{ asset('website') }}/images/translate.svg" height="23px" width="24px">
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                                <li class="@if( app()->getLocale() == 'en') active @endif">
                                    <a class="dropdown-item lang-pic d-flex align-items-center gap-1"
                                        data-lang="English" onclick="return changeLanguageByButtonClick(this)"
                                        href="javascript:void(0);">
                                        <div class="flag_img">
                                            <img width="40" height="30"
                                                src="{{ asset('website') }}/images/flags/us.png" alt="">
                                        </div>
                                        <p class="m-0 notranslate">{{ translate('home.english') }}</p>
                                    </a>
                                </li>
                                <li class="@if( app()->getLocale() == 'es') active @endif">
                                    <a class="dropdown-item lang-pic d-flex align-items-center gap-1"
                                        onclick="return changeLanguageByButtonClick(this)" data-lang="Spanish"
                                        href="javascript:void(0);">
                                        <div class="flag_img">
                                            <img width="40" height="30"
                                                src="{{ asset('website') }}/images/flags/co.png" alt="">
                                        </div>
                                        <p class="m-0 notranslate">{{ translate('home.spanish') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="nav-button">
                @auth
                    <div class="d-flex align-items-center">
                        {{-- <a href="{{ url('logout') }}" type="button" class="button me-2">
                            Logout
                        </a> --}}
                        <div>
                            @if (auth()->user()->identity_verified != 'verified')
                                <button class="btn button1 me-2 " id="not_verfied">
                                    {{ translate('home.list_your_asset') }}
                                </button>
                            @else
                                <a href="{{ route('list_asset') }}"
                                    class="btn button1 me-2 @if (auth()->user()->identity_verified != 'verified') disabled @endif">
                                    @if (auth()->user()->hasRole('user') || auth()->user()->listings()->exists())
                                        {{ translate('home.manage_your_asset') }}
                                    @else
                                        {{ translate('home.list_your_asset') }}
                                    @endif
                                </a>
                            @endif
                        </div>

                        <div class="dropdown user_img ">
                            <a class="" href="#" role="button" id="profileDropdown"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="{{ asset('website') . '/' . auth()->user()->avatar }}">
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdown">
                                <li><a class="dropdown-item" href="{{ route('bookings', ["locale" => app()->getLocale()]) }}"><span><img src="{{ asset('website') }}/images/bookings_icon.svg" alt=""></span><span>{{ translate('home.my_bookings') }}</span></a></li>
                                <li><a class="dropdown-item" href="{{ route('webaccount_setting', ["locale" => app()->getLocale()]) }}"><span><img src="{{ asset('website') }}/images/account_settings_icon.svg" alt=""></span><span>{{ translate('home.profile_settings') }}</span></a></li>
                                <li><a class="dropdown-item" href="{{ url('logout') }}"><span><img src="{{ asset('website') }}/images/log_out_red_icon.svg" alt=""></span><span style="color: #C10000;">{{ translate('home.logout') }}</span></a></li>
                            </ul>
                        </div>
                        {{-- <div class="user_img d-inline-block">
                            <a href="{{ route('webaccount_setting') }}">

                            </a>
                        </div> --}}
                    </div>
                @else
                    <div class="d-none">
                        <a href="#" class="btn button1 notranslate">{{ translate('home.list_your_asset') }}/a>
                        <div class="user_img d-inline-block">
                            <a href="{{ route('webaccount_setting', ["locale" => app()->getLocale()]) }}">
                                <img src="{{ asset('website') }}/images/user1.png">
                            </a>
                        </div>
                    </div>
                    <div class="login_btn">
                        <button type="button" class="button" data-bs-toggle="modal" data-bs-target="#signUp">
                            {{ __('website.sign_up') }}
                        </button>
                        <button type="button" class="button1 login" data-bs-toggle="modal" data-bs-target="#login">
                            {{ __('website.login') }}
                        </button>
                    </div>
                @endauth
            </div>
        </div>
    </nav>
</header>
{{-- header end --}}
