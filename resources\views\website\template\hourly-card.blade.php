<h5 class="py-sm-3 fs-30">
    {{ $currency . ' ' . number_format($listing_price, 0) }}
    {{-- <span class="fs-16">{{ translate('listing_details.hourly') }}</span> --}}
    <span class="fs-16">{{ translate('listing_details.hour') }}</span>
</h5>
<div class="info p-sm-4 p-3 mx-sm-2 m-0">
    <div class="d-flex justify-content-between divider pt-2 pb-0">
        <div class="hourly_slots">
            <p class="fs-14 m-0">{{ translate('listing_details.slots') }}</p>
            <select name="slots[]" class="form-control border-0 pt-0 ps-0 pe-4 multi_select" id="time_slots">
                <option disabled selected value="0">{{ translate('listing_details.select_slot') }}</option>
                @foreach ($listing->hourly_availabilities as $key => $hourly_availability)
                    <option value="{{ $hourly_availability->full_time }}"
                        @if (in_array($hourly_availability->full_time, $bookedSlots ?? []) ||
                                in_array($hourly_availability->full_time, $passedSlots)) disabled @endif>
                        {{ $hourly_availability->full_time }}
                    </option>
                @endforeach
            </select>
        </div>

        <div class="check_date">
            <p class="fs-14 m-0">
                @if ($listing->category_id == 3)
                    {{ translate('listing_details.pickup_dropoff_date') }}
                @else
                    {{ translate('listing_details.departure_return_date') }}
                @endif
            </p>
            <input type="hidden" name="check_in" id="check_in_inp" value="{{ $start_date }}">
            <p class="check_in_parent"> <span
                    class="check_in d-inline-block w-75">{{ date(config('constant.date_format'), strtotime($start_date)) }}</span>
                <i class="fas fa-calendar ps-3"></i>
            </p>
        </div>
    </div>
</div>
<div class="pay_info d-flex justify-content-between  pt-4 fs-16">
    <ul>
        <li>
            {{ $currency . ' ' . number_format($listing_price, 0) }} x <span class="total_hours"> 1 </span> {{ translate('listing_details.hours') }}
        </li>
    </ul>
    <ul>
        <li class="notranslate"> {{ $currency }} <span class="listing_total_amount">{{ number_format($listing_price, 0) }}</span></li>
    </ul>
</div>
{{-- new listing discount --}}
@if (isset(
        $new_listing_discount['discount_name'],
        $new_listing_discount['discount_percentage'],
        $new_listing_discount['discount_amount']))
    @php($listing_price -= $new_listing_discount['discount_amount'] * $conversion_rate)
    <div id="nl-discount-div">
        <div class="d-flex justify-content-between align-items-center">
            <p>{{ $new_listing_discount['discount_name'] }} ({{ $new_listing_discount['discount_percentage'] }}%)</p>
            <p class="nl-discount-amount text-success">-
                {{ number_format($new_listing_discount['discount_amount'] * $conversion_rate, 0) }}</p>
        </div>
    </div>
@endif
<hr class="my-1">
{{-- new listing discount end --}}
<div class="total_info  py-4 d-flex justify-content-between">
    <h5 class="semi-bold fs-16">{{ translate('listing_details.total') }}</h5>
    <p class="price semi-bold fs-16 notranslate">{{ $currency }} <span
            id="total_amount">{{ number_format($listing_price, 0) }}</span></p>
</div>
@auth
    <input type="button" id="reserve_btn" value="{{ translate('listing_details.rent_now') }}" class="button reserve" disabled>
@else
    <input type="button" value="{{ translate('listing_details.rent_now') }}" data-bs-toggle="modal" data-bs-target="#login" class="button reserve">
@endauth
