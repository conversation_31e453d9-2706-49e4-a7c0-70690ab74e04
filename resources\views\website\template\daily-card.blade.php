<h5 class="py-sm-3 fs-30">
    @if ($base_price != $listing_price)
        {{-- <del class="text-muted fs-18 light-bold">{{ $currency . number_format($base_price, 3) }}</del> <br> --}}
        {{ $currency . ' ' . number_format($listing_price, 0) }}
    @else
        {{ session('currency', 'COP') . ' ' . number_format($listing_price, 0) }}
    @endif
    {{-- {{ session('currency', 'COP') . number_format($listing_price, 0) }} --}}
    <span class="fs-16">
        @if ($listing->category_id == 4)
      {{ translate('listing_details.night') }}  
        @else
          {{ translate('listing_details.day') }}  

        @endif
    </span>
</h5>
<div class="info p-sm-4 p-3 mx-sm-2 m-0">
    {{-- @if (in_array($listing->category_id, [2]))
        <div class="input_wrapper d-flex align-items-center justify-content-between divider pb-4 gap-3 d-none">
            <div class="input_parent">
                <input type="radio" name="duration_type" id="same_day" class="duration same_day d-none"
                    {{ $totalDays == 1 ? 'checked' : '' }} disabled>
                <label for="same_day">{{ translate('listing_details.same_day') }}</label>
            </div>
            <div class="input_parent">
                <input type="radio" name="duration_type" id="multiple_day" class="duration multiple_day d-none"
                    {{ $totalDays > 1 ? 'checked' : '' }} disabled>
                <label for="multiple_day">{{ translate('listing_details.multiple_days') }}</label>
            </div>
        </div>
    @endif --}}
    <div class="d-flex justify-content-between divider py-3">
        <div class="book_timing check_date">
            <p class="fs-14 m-0">
                @if ($listing->category_id == 4)
                  {{ translate('listing_details.check_in_date') }}  
                @elseif ($listing->category_id == 3)
                    {{ $totalDays == 1 ? translate('listing_details.pickup_dropoff_date') : translate('listing_details.pickup_date') }}
                @elseif ($listing->category_id == 2)
                    {{ $totalDays == 1 ? translate('listing_details.departure_return_date') : translate('listing_details.departure_date') }}
                @else
                   {{ translate('listing_details.start_date') }}  
                @endif
            </p>
            <div class="d-flex align-items-center pe-2 justify-content-between">
                <input type="hidden" name="check_in" value="{{ $start_date }}" />
                <span
                    class="check_in border-0">{{ $start_date ? date(config('constant.date_format'), strtotime($start_date)) : translate('listing_details.select_a_date') }}</span>
                <i class="far fa-calendar"></i>
            </div>
            </p>
        </div>
        <div class="check-cont book_timing check_date {{ $totalDays > 0 ? '' : 'd-none' }}">
            <p class="fs-14 m-0 ">
                @if ($listing->category_id == 4)
                  {{ translate('listing_details.check_out_date') }}  
                @elseif ($listing->category_id == 3)
                {{ translate('listing_details.drop_off_date') }}  
                @elseif ($listing->category_id == 2)
                 {{ translate('listing_details.return_date') }}  
                @else
                   {{ translate('listing_details.end_date') }} 
                @endif
            </p>
            <div class="d-flex align-items-center pe-2 justify-content-between">
                <input type="hidden" value="{{ $end_date ?? translate('listing_details.select_a_date') }}" name="check_out" />
                <span
                    class="check_in border-0">{{ $end_date ? date(config('constant.date_format'), strtotime($end_date)) : translate('listing_details.select_a_date') }}</span>
                <i class="far fa-calendar"></i>
            </div>
        </div>
    </div>
    @if (in_array($listing->category_id, [2, 4]))
        <div>
            <p class="fs-14 pt-3 m-0">
                {{ translate('listing_details.guest') }}  
            </p>
            {{-- <input type="number" name="guest" class="form-control border-0 border-bottom" id="guest" min="1" max="{{$listing->detail->guests}}" value="1"> --}}
            <div class="guest_wrapper d-flex gap-2 align-items-center">
                <button class="btn minus_btn p-0 " type="button"
                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                    <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <input type="number" name="guest" class="form-control border-0 text-center" id="guest"
                    min="1" max="@if($listing->category_id == 4){{ $listing->detail->guests  ?? '1' }}@else{{ $listing->detail->capacity  ?? '1' }}@endif" value="1" readonly />
                <button class="btn plus_btn p-0" type="button"
                    onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    @elseif (in_array($listing->category_id, [3]))
        <div class="d-flex justify-content-between divider pt-3 pb-4 timing_slots">
            <div class="book_timing">
                <label for="check_in_time" class="fs-14 mb-1"> {{ translate('listing_details.pickup_time') }} </label>
                <div class="d-flex align-items-center justify-content-between">
                    <input type="time" value="" class="border-0 w-100 time_picker" name="check_in_time"
                        id="check_in_time" placeholder="{{ translate('listing_details.enter_pickup_time') }}" />
                    <label for="check_in_time"><i class="far fa-clock"></i></label>
                </div>
            </div>
            <div class=" book_timing">
                <label for="check_out_time" class="fs-14 mb-1 "> {{ translate('listing_details.drop_off_time') }} </label>
                <div class="d-flex align-items-center justify-content-between">
                    <input type="time" value="" class="border-0 w-100 time_picker" name="check_out_time"
                        id="check_out_time" placeholder="{{ translate('listing_details.enter_dropoff_time') }}" />
                    <label for="check_out_time"><i class="far fa-clock"></i></label>
                </div>
            </div>
        </div>
    @endif
</div>
<div class="divider pt-4 fs-16">
    {{-- listing total --}}
    <div class="d-flex justify-content-between align-items-center">
        <p>
            {{ $currency . ' ' . number_format($listing_price, 0) }}
            x <span class="total_days">{{ $totalDays }}</span>
            @if ($listing->category_id == 4)
                {{ translate('listing_details.night') }}{{ $totalDays > 1 ? 's' : '' }}
            @else
                {{ translate('listing_details.day') }}{{ $totalDays > 1 ? 's' : '' }}
            @endif
        </p>
        <p class="notranslate">{{ $currency . ' ' . number_format($listing_total, 0) }}</p>
    </div>
    {{-- listing total end --}}
    {{-- New listing discount --}}
    @if (($new_listing_discount['discount_amount'] ?? null) > 0)
        @php
            $listing_total -= $new_listing_discount['discount_amount'];
        @endphp
        <div class="d-flex justify-content-between align-items-center">
            <p>
                {{ translate('listing_details.new_listing_discount') }} ({{ $new_listing_discount['discount_percentage'] ?? 0 }}%)
            </p>
            <p class="new_listing_discount text-success">-
                {{ number_format($new_listing_discount['discount_amount'], 0) }}</p>
        </div>
    @endif
    {{-- New listing discount end --}}
    {{-- weekly/monthly discount  --}}
    @if ($weekly_monthly_discount['discount_name'])
        @php
            $listing_total -= $weekly_monthly_discount['discount_amount'];
        @endphp
        <div class="d-flex justify-content-between align-items-center">
            <p>
                {{ $weekly_monthly_discount['discount_name'] }}
                ({{ $weekly_monthly_discount['discount_percentage'] }}%)
            </p>
            <p class="weekly_discount text-success">-
                {{ number_format($weekly_monthly_discount['discount_amount'], 0) }}</p>
        </div>
    @endif
    {{-- weekly/monthly discount end --}}
</div>
<div class="total_info py-4 d-flex justify-content-between">
    <h5 class="semi-bold fs-16">{{ translate('listing_details.total') }}</h5>
    <p class="price semi-bold fs-16 notranslate">{{ $currency }} <span
            class="total_amount">{{ number_format($listing_total, 0) }}</span></p>
</div>
@auth 
    <input type="button" id="reserve_btn" value="@if (in_array($listing->category_id, [3, 2])) {{ translate('listing_details.rent_now') }} @else {{ translate('listing_details.book_now') }} @endif"
        class="button reserve" @if (
            ($listing->detail->minimum_stay_length ?? 0) > $totalDays ||
                !isset($start_date, $end_date) ||
                ($listing->category_id == 4 && $totalDays == 0)) disabled @endif>
@else
    <input type="button" value="{{ translate('listing_details.book_now') }}" data-bs-toggle="modal" data-bs-target="#login" class="button reserve"
        style="background-color: white; border-color: #000">
@endauth
