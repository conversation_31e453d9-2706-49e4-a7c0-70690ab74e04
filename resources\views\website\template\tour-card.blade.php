<input type="hidden" name="listing_id" value="{{ $listing->ids }}">
<input type="hidden" name="check_in" value="{{ $start_date }}">
<input type="hidden" name="basis_type" value="Tour">
<div class="info px-4 pt-sm-4 pb-sm-5 py-3 px-4 booking_info">
    <div class="d-flex align-items-top justify-content-between">
        <div>
            <h5 class="py-1 fs-30 price">
                {{ $currency }}
                @if ($tour_type == "guests" || !isset($tour_type) ? translate('listing_details.checked')   : "")
                    {{ number_format($adult_price_base * $conversion_rate, 0) }}
                @else
                    {{ number_format($private_price_base * $conversion_rate, 0) }}
                @endif

                <span class="fs-16 type_guest"> {{ translate('listing_details.guest') }}</span>
                <span class="fs-16 type_private"> {{translate('listing_details.tour')  }}</span>
            </h5>
        </div>
    </div>
    <div class="info p-sm-4 p-3 mx-sm-2 m-0 tour_type">
        <div class="input_wrapper d-flex align-items-center justify-content-between divider pb-4 gap-3">
            <div class="input_parent">
                <input type="radio" name="tour_type" value="guests" id="guests" class="duration tour_duration_type same_day d-none" {{$tour_type == "guests" || !isset($tour_type) || $active_booking > 0 ? "checked" : ""}}>
                <label for="guests">{{ translate('listing_details.guests') }}</label>
            </div>
            @if ($listing->detail->private_booking == "yes" && $active_booking == 0)
                <div class="input_parent">
                    <input type="radio" name="tour_type" value="private" id="private" class="duration tour_duration_type multiple_day d-none" {{$tour_type == "private" ? "checked" : ""}}>
                    <label for="private">{{ translate('listing_details.private_booking') }}</label>
                </div>
            {{-- @else
                <div class="input_parent">
                    <input type="radio" class="duration multiple_day d-none" disabled>
                    <label for="private">Private Booking</label>
                </div> --}}
            @endif
        </div>
        <div class="divider py-3">
            <p class="fs-14 m-0">{{ translate('listing_details.date')   }}</p>
            <p class="m-0"><span class="check_in d-inline-block">
                {{ $start_date ? date(config('constant.date_format'), strtotime($start_date)) : '- -'  }}
            </span> <i
                    class="far fa-calendar ps-3"></i>
            </p>
        </div>
        @php
            $total_adult = $adult_number + $child_number;
        @endphp
        <div class="guests row pt-3" data-capacity="{{ $remaining_capacity ?? 0 }}">
            <div class="col-md-6">
                <p class="fs-14 m-0">{{ translate('listing_details.adults')   }}</p>
                <div class="guest_wrapper d-flex gap-2 align-items-center">
                    <button class="btn minus_btn p-0 " id="adult_remove" type="button"> <i class="fa fa-minus"
                            aria-hidden="true"></i></button>
                    <input class="number quantity tank m-0 d-flex px-1 text-center adult_input border-0"
                        id="adult_number" min="1" value="{{ $adult_number }}" name="adult_number" type="number"
                        readonly />
                    <button class="btn plus_btn p-0" id="adult_add" {{ $total_adult >= $remaining_capacity ? translate('listing_details.disabled') : ""  }} type="button"><i class="fa fa-plus"
                            aria-hidden="true"></i></button>
                </div>
            </div>
            @if ($listing->detail->child_allow == 'yes')
                <div class="col-md-6">
                    <p class="fs-14 m-0">{{ translate('listing_details.children') }}</p>
                    <div class="guest_wrapper child_wrapper d-flex gap-2 align-items-center">
                        <button class="btn minus_btn p-0 " id="child_remove" type="button"> <i class="fa fa-minus"
                                aria-hidden="true"></i></button>
                        <input class="number quantity tank m-0 d-flex px-1 text-center child_input border-0"
                            min="0" value="{{ $child_number }}" name="child_number" type="number"
                            id="child_number" readonly />
                        <button class="btn plus_btn p-0" id="child_add" {{ $total_adult >= $remaining_capacity ? translate('listing_details.disabled') : ""  }} type="button"><i class="fa fa-plus"
                                aria-hidden="true"></i></button>
                    </div>
                </div>
            @endif
        </div>
    </div>
    <div class="pay_info pt-4 fs-16 divider">
        @if ($tour_type == "guests" || $active_booking > 0)
            {{-- Adult Price --}}
            <div class="d-flex align-items-center justify-content-between mb-1">
                <p>{{ $currency . ' ' . number_format(($adult_price_base * $conversion_rate)) }} x <span class="adult-qty">{{ $adult_number }}</span>
                    {{ translate('listing_details.adult')   }}</p>
                <p class="notranslate adult_total_price notranslate">{{ $currency . ' ' . number_format(($adult_price_base * $conversion_rate) * $adult_number) }}</p>
            </div>
            {{-- Adult Price End --}}

            {{-- Child Price --}}
            @if ($listing->detail->child_allow == 'yes')
                <div class="d-flex align-items-center justify-content-between mb-1 child_price_parent d-none">
                    <p>{{ $currency . ' ' . number_format(($child_price_base * $conversion_rate)) }} x <span
                            class="child-qty">{{ $child_number }}</span> {{ translate('listing_details.child') }}</p>
                    <p class="child_total_price notranslate">{{ $currency . ' ' . number_format(($child_price_base * $conversion_rate) * $child_number) }}</p>
                </div>
            @endif
            {{-- Child Price End --}}
        @else
            <div class="d-flex align-items-center justify-content-between mb-1">
                <p>{{ translate('listing_details.private_booking')}}</p>
                <p class="notranslate">{{ $currency . ' ' . number_format($private_price_base * $conversion_rate) }}</p>
            </div>
        @endif

        {{-- New Listing Discount --}}
        @if (isset($new_listing_discount['discount_name'],$new_listing_discount['discount_percentage'],$new_listing_discount['discount_amount']))
            @php($total_price -= $new_listing_discount['discount_amount'])
            <div class="d-flex justify-content-between align-items-center">
                <p>{{ $new_listing_discount['discount_name'] }} ({{ $new_listing_discount['discount_percentage'] }}%)</p>
                <p class="{{ $tour_type == "private" ? "" : "nl-discount-amount" }}  text-success">
                    - {{ number_format($new_listing_discount['discount_amount'] * $conversion_rate, 0) }}</p>
            </div>
        @endif
        {{-- New Listing Discount End --}}
    </div>
    <div class="total_info py-4 d-flex justify-content-between">
        <h5 class="semi-bold fs-16">{{ translate('listing_details.total') }}</h5>
        <p class="notranslate price semi-bold fs-16 total_amount notranslate" 
        {{-- id="{{ $tour_type == "guests" || $active_booking > 0 ? translate('listing_details.subtotal_amount')   : "" }}" --}}
        id="subtotal_amount"
        >
            {{ $currency . ' ' . number_format($total_price * $conversion_rate) }}
        </p>
    </div>
    @auth
        <input type="button" id="reserve_btn" value="{{ translate('listing_details.reserve_now') }} " class="button reserve" {{ !isset($start_date) ? translate('listing_details.disabled')   : "" }} >
    @else
        <input type="button" value="{{ translate('listing_details.reserve_now') }}" data-bs-toggle="modal" data-bs-target="#login" class="button reserve">
    @endauth
</div>
